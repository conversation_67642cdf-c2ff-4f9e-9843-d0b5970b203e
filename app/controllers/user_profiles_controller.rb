class UserProfilesController < ApplicationController
  before_action :authenticate_user! 
  before_action :set_user_profile, only: %i[ edit update ]

  # GET /user_profile
  def show
    @user_profile = UserProfile.find(params[:id])
    
    #not working fuckingactionpolicyagain
    #@can_view_contact_details = allowed_to?(:view_contact_details?, @user_profile)
    
    @is_connected = @user_profile.connected_with?(current_user.id)
    #@visible_profile_data = @user_profile.visible_attributes_for(current_user.id)
  end

  # GET /user_profile/edit
  def edit
  end

  def update
    if @user_profile.update(user_profile_params)
      # Send admin notification if profile is complete and user is not yet approved
      if !current_user.approved? && @user_profile.required_fields_present?
        NotificationMailer.user_approval_request_notification(current_user).deliver_later
      end

      # For first time users who just completed their profile, redirect to projects (only if approved)
      if current_user.sign_in_count == 1 && @user_profile.required_fields_present? && current_user.approved?
        redirect_to projects_path,
          notice: t('user_profiles.edit.success',
          default: "User profile was successfully updated."),
          status: :see_other
      else
        # For subsequent updates, stay on the edit page
        redirect_to edit_user_profile_path(@user_profile),
          notice: t('user_profiles.edit.success',
          default: "User profile was successfully updated."),
          status: :see_other
      end
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def set_language
    new_locale = params[:locale_code].to_s.strip.to_sym

    I18n.locale = new_locale
    current_user.user_profile.update(default_language: new_locale.to_s)

    # Get the current path without locale prefix
    referer = request.referer
    if referer.present?
      begin
        uri = URI.parse(referer)
        path_without_locale = uri.path.gsub(%r{^/[a-z]{2}(/|$)}, '/')
        path_without_locale = '/' if path_without_locale.blank?

        # Reconstruct the URL with the new locale
        redirect_to "/#{new_locale}#{path_without_locale}"
      rescue URI::InvalidURIError
        # If there's an issue parsing the referer, fall back to root
        redirect_to root_path
      end
    else
      redirect_to root_path
    end
  end

  # Subscription selection methods
  def subscription_select
    # Show subscription selection page
  end

  def subscription_choose
    tier = params[:tier]
    referral_code = params[:referral_code]

    case tier
    when 'free'
      # User chooses free tier - update immediately
      current_user.update!(subscription_tier: :free)
      session[:subscription_selected] = true
      redirect_to root_path, notice: "Welcome! You're now on the free tier. You can upgrade anytime."
    when 'premium'
      # Store selection for payment team
      session[:selected_tier] = 'premium'
      session[:referral_code] = referral_code if referral_code.present?
      session[:subscription_selected] = true

      # Placeholder redirect - payment team will replace this
      redirect_to root_path, notice: "Premium subscription selected! Payment integration coming soon."
    else
      redirect_to select_subscription_path, alert: "Invalid subscription tier selected"
    end
  end

  def validate_referral_code
    code = params[:code]&.strip&.upcase

    if code.blank?
      render json: { success: false, message: "Please enter a referral code" }
      return
    end

    referral_code = ReferralCode.find_by(code: code)

    if referral_code.nil?
      render json: { success: false, message: "Invalid referral code" }
      return
    end

    if !referral_code.active? || referral_code.expires_at&.past?
      render json: { success: false, message: "This referral code has expired" }
      return
    end

    if referral_code.current_uses >= referral_code.max_uses
      render json: { success: false, message: "This referral code has been used up" }
      return
    end

    # Calculate discount for premium tier (€29.99)
    premium_price = 29.99
    discount = calculate_referral_discount(referral_code, premium_price)

    render json: {
      success: true,
      message: "Referral code applied successfully!",
      discount: discount
    }
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_user_profile
      @user_profile = current_user.user_profile
    end

    # Only allow a list of trusted parameters through.
    def user_profile_params
      params.require(:user_profile).permit(:first_name, :last_name, :email, :bio, :phone, :city, :country)
    end

    # Calculate referral code discount for display purposes
    def calculate_referral_discount(referral_code, base_price)
      # Simple calculation - for free tier upgrades, show 100% discount
      # Payment team will implement more sophisticated discount types
      if referral_code.tier_upgrade_to == 'premium'
        {
          type: 'free_upgrade',
          final_price: 0,
          original_price: base_price,
          description: "Free Premium for #{referral_code.duration_months} month#{'s' if referral_code.duration_months > 1}"
        }
      else
        {
          type: 'none',
          final_price: base_price,
          original_price: base_price,
          description: 'No discount available'
        }
      end
    end
end
