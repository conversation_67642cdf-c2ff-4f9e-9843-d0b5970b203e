<% content_for :title, "Choose Your Subscription" %>

<div class="container mt-4">
  <div class="row justify-content-center">
    <div class="col-lg-10">
      <div class="text-center mb-5">
        <h1 class="display-4 mb-3">Choose Your Plan</h1>
        <p class="lead text-muted">Select the subscription tier that best fits your needs</p>
      </div>

      <!-- Referral Code Input -->
      <div class="row justify-content-center mb-4">
        <div class="col-md-6">
          <div class="card border-info">
            <div class="card-body text-center">
              <h6 class="card-title">
                <i class="fas fa-gift text-info"></i> Have a Referral Code?
              </h6>
              <div class="d-flex">
                <input type="text" id="referral-code-input" class="form-control mr-2" 
                       placeholder="Enter code here" style="text-transform: uppercase; letter-spacing: 1px;">
                <button type="button" id="apply-code-btn" class="btn btn-info">Apply</button>
              </div>
              
              <div id="referral-feedback" class="mt-3" style="display: none;"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Subscription Tiers -->
      <div class="row justify-content-center">
        <!-- Free Tier -->
        <div class="col-lg-5 mb-4">
          <div class="card h-100 <%= 'border-primary' if current_user.tier_free? %>">
            <div class="card-header text-center bg-light">
              <h4 class="card-title mb-0">Free</h4>
              <div class="h2 text-muted">€0<small>/month</small></div>
            </div>
            <div class="card-body">
              <ul class="list-unstyled">
                <li class="mb-2">
                  <i class="fas fa-check text-success"></i> View projects and wants
                </li>
                <li class="mb-2">
                  <i class="fas fa-check text-success"></i> Basic network access
                </li>
                <li class="mb-2">
                  <i class="fas fa-check text-success"></i> Limited features
                </li>
                <li class="mb-2">
                  <i class="fas fa-times text-muted"></i> Cannot create projects
                </li>
                <li class="mb-2">
                  <i class="fas fa-times text-muted"></i> Cannot upload files
                </li>
              </ul>
            </div>
            <div class="card-footer text-center">
              <% if current_user.tier_free? %>
                <button class="btn btn-outline-secondary" disabled>Current Plan</button>
              <% else %>
                <%= form_with url: choose_subscription_path, method: :post, local: true do |form| %>
                  <%= form.hidden_field :tier, value: 'free' %>
                  <%= form.submit "Choose Free", class: "btn btn-outline-primary" %>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Premium Tier -->
        <div class="col-lg-5 mb-4">
          <div class="card h-100 border-warning <%= 'border-primary' if current_user.tier_premium? %>">
            <div class="card-header text-center bg-warning text-dark">
              <h4 class="card-title mb-0">
                Premium
                <span class="badge badge-light text-warning ml-2">RECOMMENDED</span>
              </h4>
              <div id="premium-pricing">
                <div class="h2">€29.99<small>/month</small></div>
              </div>
            </div>
            <div class="card-body">
              <ul class="list-unstyled">
                <li class="mb-2">
                  <i class="fas fa-check text-success"></i> All Free features
                </li>
                <li class="mb-2">
                  <i class="fas fa-check text-success"></i> Create unlimited projects
                </li>
                <li class="mb-2">
                  <i class="fas fa-check text-success"></i> Upload files
                </li>
                <li class="mb-2">
                  <i class="fas fa-check text-success"></i> Full network access
                </li>
                <li class="mb-2">
                  <i class="fas fa-check text-success"></i> Priority support
                </li>
              </ul>
            </div>
            <div class="card-footer text-center">
              <% if current_user.tier_premium? %>
                <button class="btn btn-warning" disabled>Current Plan</button>
              <% else %>
                <button type="button" id="choose-premium-btn" class="btn btn-warning">
                  Choose Premium
                </button>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <!-- Additional Information -->
      <div class="row mt-5">
        <div class="col-12">
          <div class="card bg-light">
            <div class="card-body">
              <h5 class="card-title">
                <i class="fas fa-info-circle text-info"></i> Important Information
              </h5>
              <div class="row">
                <div class="col-md-6">
                  <ul class="list-unstyled">
                    <li><i class="fas fa-shield-alt text-success"></i> Secure payment processing</li>
                    <li><i class="fas fa-sync-alt text-info"></i> Cancel anytime</li>
                    <li><i class="fas fa-credit-card text-primary"></i> Monthly billing</li>
                  </ul>
                </div>
                <div class="col-md-6">
                  <ul class="list-unstyled">
                    <li><i class="fas fa-envelope text-warning"></i> Email support included</li>
                    <li><i class="fas fa-mobile-alt text-success"></i> Mobile-friendly platform</li>
                    <li><i class="fas fa-lock text-danger"></i> Data privacy guaranteed</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Back to Dashboard -->
      <div class="text-center mt-4">
        <%= link_to "← Back to Dashboard", root_path, class: "btn btn-outline-secondary" %>
      </div>
    </div>
  </div>
</div>

<!-- Hidden form for premium selection -->
<%= form_with url: choose_subscription_path, method: :post, local: true, id: "premium-form", style: "display: none;" do |form| %>
  <%= form.hidden_field :tier, value: 'premium' %>
  <%= form.hidden_field :referral_code, id: "premium-referral-code" %>
<% end %>

<script>
let appliedReferralCode = null;
let originalPremiumPrice = 29.99;

document.getElementById('apply-code-btn').addEventListener('click', function() {
  const code = document.getElementById('referral-code-input').value.trim().toUpperCase();
  
  if (!code) {
    showFeedback('Please enter a referral code', 'danger');
    return;
  }
  
  // Show loading state
  this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
  this.disabled = true;
  
  // AJAX call to validate referral code
  fetch('<%= validate_referral_code_path %>', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    },
    body: JSON.stringify({ code: code })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      appliedReferralCode = code;
      updatePremiumPricing(data.discount);
      showFeedback(data.message, 'success');
    } else {
      showFeedback(data.message, 'danger');
    }
  })
  .catch(error => {
    showFeedback('Error validating code. Please try again.', 'danger');
  })
  .finally(() => {
    // Reset button state
    document.getElementById('apply-code-btn').innerHTML = 'Apply';
    document.getElementById('apply-code-btn').disabled = false;
  });
});

function showFeedback(message, type) {
  const feedback = document.getElementById('referral-feedback');
  feedback.className = `alert alert-${type} mt-3`;
  feedback.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i> ${message}`;
  feedback.style.display = 'block';
}

function updatePremiumPricing(discount) {
  const pricingDiv = document.getElementById('premium-pricing');
  
  if (discount.final_price === 0) {
    // Free upgrade
    pricingDiv.innerHTML = `
      <div class="h2 text-success">FREE<small>/month</small></div>
      <small class="text-muted"><s>€${originalPremiumPrice}</s> - ${discount.description}</small>
    `;
  } else {
    // Discounted price
    pricingDiv.innerHTML = `
      <div class="h2">€${discount.final_price}<small>/month</small></div>
      <small class="text-muted"><s>€${originalPremiumPrice}</s> - ${discount.description}</small>
    `;
  }
}

document.getElementById('choose-premium-btn').addEventListener('click', function() {
  // Set referral code in hidden form if applied
  if (appliedReferralCode) {
    document.getElementById('premium-referral-code').value = appliedReferralCode;
  }
  
  // Submit the premium form (will be handled by subscription team's payment flow)
  document.getElementById('premium-form').submit();
});

// Allow Enter key to apply referral code
document.getElementById('referral-code-input').addEventListener('keypress', function(e) {
  if (e.key === 'Enter') {
    document.getElementById('apply-code-btn').click();
  }
});
</script>

<style>
  .card {
    transition: transform 0.2s ease-in-out;
  }
  
  .card:hover {
    transform: translateY(-5px);
  }
  
  .card-header {
    border-bottom: 2px solid rgba(0,0,0,0.1);
  }
  
  .list-unstyled li {
    padding: 0.25rem 0;
  }
  
  .badge {
    font-size: 0.7rem;
  }
</style>
