<% content_for :title, "Choose Your Subscription" %>

<div class="container">
  <div class="flex flex-column g1 align-start">
    <h1>Choose Your Plan</h1>
    <p>Select the subscription tier that best fits your needs</p>

    <!-- Referral Code Input -->
    <div class="form-card">
      <h3>Have a Referral Code?</h3>
      <div class="flex g1 align-center">
        <input type="text" id="referral-code-input" class="form-input"
               placeholder="Enter code here" style="text-transform: uppercase; letter-spacing: 1px; flex: 1;">
        <button type="button" id="apply-code-btn" class="button">Apply</button>
      </div>

      <div id="referral-feedback" style="display: none; margin-top: 1rem;"></div>
    </div>

    <!-- Subscription Tiers -->
    <div class="flex g1">
      <!-- Free Tier -->
      <div class="card <%= 'current-plan' if current_user.tier_free? %>">
        <div class="subscription-header">
          <h3>Free</h3>
          <div class="price">€0<span class="period">/month</span></div>
        </div>
        <div class="subscription-features">
          <ul>
            <li class="feature-included">✓ View projects and wants</li>
            <li class="feature-included">✓ Basic network access</li>
            <li class="feature-included">✓ Limited features</li>
            <li class="feature-excluded">✗ Cannot create projects</li>
            <li class="feature-excluded">✗ Cannot upload files</li>
          </ul>
        </div>
        <div class="subscription-action">
          <% if current_user.tier_free? %>
            <button class="button button-secondary" disabled>Current Plan</button>
          <% else %>
            <%= form_with url: choose_subscription_path, method: :post, local: true do |form| %>
              <%= form.hidden_field :tier, value: 'free' %>
              <%= form.submit "Choose Free", class: "button" %>
            <% end %>
          <% end %>
        </div>
      </div>

      <!-- Premium Tier -->
      <div class="card premium-card <%= 'current-plan' if current_user.tier_premium? %>">
        <div class="subscription-header premium-header">
          <h3>
            Premium
            <span class="tag attention">RECOMMENDED</span>
          </h3>
          <div id="premium-pricing" class="price">
            €29.99<span class="period">/month</span>
          </div>
        </div>
        <div class="subscription-features">
          <ul>
            <li class="feature-included">✓ All Free features</li>
            <li class="feature-included">✓ Create unlimited projects</li>
            <li class="feature-included">✓ Upload files</li>
            <li class="feature-included">✓ Full network access</li>
            <li class="feature-included">✓ Priority support</li>
          </ul>
        </div>
        <div class="subscription-action">
          <% if current_user.tier_premium? %>
            <button class="button button-secondary" disabled>Current Plan</button>
          <% else %>
            <button type="button" id="choose-premium-btn" class="button button-primary">
              Choose Premium
            </button>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Additional Information -->
    <div class="form-card bg-light">
      <h3>Important Information</h3>
      <div class="flex g1">
        <div class="flex flex-column">
          <ul class="info-list">
            <li>🛡️ Secure payment processing</li>
            <li>🔄 Cancel anytime</li>
            <li>💳 Monthly billing</li>
          </ul>
        </div>
        <div class="flex flex-column">
          <ul class="info-list">
            <li>📧 Email support included</li>
            <li>📱 Mobile-friendly platform</li>
            <li>🔒 Data privacy guaranteed</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Back to Dashboard -->
    <div class="text-c">
      <%= link_to "← Back to Dashboard", root_path, class: "text-link" %>
    </div>
  </div>
</div>

<!-- Hidden form for premium selection -->
<%= form_with url: choose_subscription_path, method: :post, local: true, id: "premium-form", class: "hidden" do |form| %>
  <%= form.hidden_field :tier, value: 'premium' %>
  <%= form.hidden_field :referral_code, id: "premium-referral-code" %>
<% end %>

<script>
let appliedReferralCode = null;
let originalPremiumPrice = 29.99;

document.getElementById('apply-code-btn').addEventListener('click', function() {
  const code = document.getElementById('referral-code-input').value.trim().toUpperCase();

  if (!code) {
    showFeedback('Please enter a referral code', 'error');
    return;
  }

  // Show loading state
  this.textContent = 'Applying...';
  this.disabled = true;

  // AJAX call to validate referral code
  fetch('<%= validate_referral_code_path %>', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    },
    body: JSON.stringify({ code: code })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      appliedReferralCode = code;
      updatePremiumPricing(data.discount);
      showFeedback(data.message, 'success');
    } else {
      showFeedback(data.message, 'error');
    }
  })
  .catch(error => {
    showFeedback('Error validating code. Please try again.', 'error');
  })
  .finally(() => {
    // Reset button state
    document.getElementById('apply-code-btn').textContent = 'Apply';
    document.getElementById('apply-code-btn').disabled = false;
  });
});

function showFeedback(message, type) {
  const feedback = document.getElementById('referral-feedback');
  feedback.className = type === 'success' ? 'tag ready' : 'tag attention';
  feedback.textContent = message;
  feedback.style.display = 'block';
}

function updatePremiumPricing(discount) {
  const pricingDiv = document.getElementById('premium-pricing');

  if (discount.final_price === 0) {
    // Free upgrade
    pricingDiv.innerHTML = `
      FREE<span class="period">/month</span>
      <div class="original-price">€${originalPremiumPrice} - ${discount.description}</div>
    `;
  } else {
    // Discounted price
    pricingDiv.innerHTML = `
      €${discount.final_price}<span class="period">/month</span>
      <div class="original-price">€${originalPremiumPrice} - ${discount.description}</div>
    `;
  }
}

document.getElementById('choose-premium-btn').addEventListener('click', function() {
  // Set referral code in hidden form if applied
  if (appliedReferralCode) {
    document.getElementById('premium-referral-code').value = appliedReferralCode;
  }

  // Submit the premium form (will be handled by subscription team's payment flow)
  document.getElementById('premium-form').submit();
});

// Allow Enter key to apply referral code
document.getElementById('referral-code-input').addEventListener('keypress', function(e) {
  if (e.key === 'Enter') {
    document.getElementById('apply-code-btn').click();
  }
});
</script>

<style>
  .subscription-header {
    text-align: center;
    padding: 1.5rem 1rem;
    border-bottom: 1px solid #e0e0e0;
  }

  .subscription-header h3 {
    margin-bottom: 0.5rem;
    color: #282828;
  }

  .premium-header {
    background-color: #FFF9E8;
    border-bottom-color: #FFD06B;
  }

  .price {
    font-size: 2rem;
    font-weight: 600;
    color: #2271B1;
  }

  .period {
    font-size: 1rem;
    color: #555555;
  }

  .original-price {
    font-size: 0.9rem;
    color: #666;
    text-decoration: line-through;
    margin-top: 0.25rem;
  }

  .subscription-features {
    padding: 1.5rem 1rem;
  }

  .subscription-features ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .subscription-features li {
    padding: 0.5rem 0;
    font-size: 1rem;
  }

  .feature-included {
    color: #209903;
  }

  .feature-excluded {
    color: #999;
  }

  .subscription-action {
    padding: 1rem;
    text-align: center;
    border-top: 1px solid #e0e0e0;
  }

  .current-plan {
    border: 2px solid #17B7B7;
  }

  .premium-card {
    border: 2px solid #FFD06B;
  }

  .info-list {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .info-list li {
    padding: 0.25rem 0;
    font-size: 0.9rem;
  }

  .form-input {
    min-width: 200px;
  }

  @media (max-width: 1080px) {
    .flex.g1 {
      flex-direction: column;
    }

    .card {
      width: 100%;
      margin-bottom: 1rem;
    }
  }
</style>
