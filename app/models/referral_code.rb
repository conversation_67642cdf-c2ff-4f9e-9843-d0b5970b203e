# ABOUTME: ReferralCode model for subscription tier upgrades with usage tracking
# ABOUTME: Includes validation, status management, and secure redemption logic with race condition protection
class ReferralCode < ApplicationRecord
  belongs_to :created_by, class_name: 'User'
  
  enum status: { active: 0, expired: 1, used_up: 2, disabled: 3 }
  enum tier_upgrade_to: { premium: 1, pilot: 2 }
  
  validates :code, presence: true, uniqueness: true, length: { maximum: 12 }
  validates :max_uses, presence: true, numericality: { greater_than: 0 }
  validates :duration_months, presence: true, numericality: { greater_than: 0 }
  
  scope :available, -> { where(status: :active).where('expires_at > ? OR expires_at IS NULL', Time.current) }
  
  # Simplified redemption with race condition protection
  def redeem!(user)
    ReferralCode.transaction do
      lock! # Prevent race conditions
      
      raise Errors::CodeExpiredError if expires_at&.past?
      raise Errors::CodeLimitReachedError if current_uses >= max_uses
      raise Errors::CodeNotActiveError unless active?
      raise Errors::UserAlreadyPremiumError if user.active_subscription?
      
      # Apply tier upgrade
      expiry_date = duration_months.months.from_now
      user.update!(
        subscription_tier: tier_upgrade_to,
        subscription_expires_at: tier_upgrade_to == 'pilot' ? nil : expiry_date
      )
      
      # Update usage tracking
      increment!(:current_uses)
      update!(status: :used_up) if current_uses >= max_uses
    end
    true
  end
end
