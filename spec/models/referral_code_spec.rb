require 'rails_helper'

RSpec.describe ReferralCode, type: :model do
  let(:admin_user) { create(:user, role: :super_boss) }
  let(:free_user) { create(:user, subscription_tier: :free) }
  let(:premium_user) { create(:user, subscription_tier: :premium, subscription_expires_at: 1.month.from_now) }
  
  describe "validations" do
    subject { build(:referral_code, created_by: admin_user) }
    
    it "validates presence of code" do
      subject.code = nil
      expect(subject).not_to be_valid
      expect(subject.errors[:code]).to be_present
    end
    
    it "validates uniqueness of code" do
      create(:referral_code, code: "TESTCODE", created_by: admin_user)
      subject.code = "TESTCODE"
      expect(subject).not_to be_valid
      expect(subject.errors[:code]).to be_present
    end
    
    it "validates code length" do
      subject.code = "A"  # short codes now allowed
      expect(subject).to be_valid
      
      subject.code = "A" * 13  # too long
      expect(subject).not_to be_valid
      
      subject.code = "ABCDEF"  # normal length
      expect(subject).to be_valid
    end
    
    it "validates max_uses is positive" do
      subject.max_uses = 0
      expect(subject).not_to be_valid
      expect(subject.errors[:max_uses]).to be_present
    end
    
    it "validates duration_months is positive" do
      subject.duration_months = 0
      expect(subject).not_to be_valid
      expect(subject.errors[:duration_months]).to be_present
    end
  end
  
  describe "enums" do
    it "defines status enum" do
      expect(ReferralCode.statuses.keys).to eq(%w[active expired used_up disabled])
    end
    
    it "defines tier_upgrade_to enum" do
      expect(ReferralCode.tier_upgrade_tos.keys).to eq(%w[premium pilot])
    end
  end
  
  describe "scopes" do
    let!(:active_code) { create(:referral_code, status: :active, expires_at: 1.month.from_now, created_by: admin_user) }
    let!(:expired_code) { create(:referral_code, status: :active, expires_at: 1.day.ago, created_by: admin_user) }
    let!(:disabled_code) { create(:referral_code, status: :disabled, created_by: admin_user) }
    
    describe ".available" do
      it "returns only active codes that haven't expired" do
        available_codes = ReferralCode.available
        expect(available_codes).to include(active_code)
        expect(available_codes).not_to include(expired_code)
        expect(available_codes).not_to include(disabled_code)
      end
    end
  end
  
  describe "#redeem!" do
    let(:code) { create(:referral_code, max_uses: 1, current_uses: 0, tier_upgrade_to: :premium, duration_months: 3, created_by: admin_user) }
    
    it "successfully redeems valid code for free user" do
      expect { code.redeem!(free_user) }.to change { free_user.reload.subscription_tier }.to("premium")
      expect(free_user.subscription_expires_at).to be_within(1.minute).of(3.months.from_now)
      expect(code.reload.current_uses).to eq(1)
    end
    
    it "marks code as used_up when max uses reached" do
      expect { code.redeem!(free_user) }.to change { code.reload.status }.to("used_up")
    end
    
    it "raises error for expired code" do
      code.update!(expires_at: 1.day.ago)
      expect { code.redeem!(free_user) }.to raise_error(Errors::CodeExpiredError)
    end
    
    it "raises error when usage limit reached" do
      code.update!(current_uses: 1)  # max_uses = 1
      expect { code.redeem!(free_user) }.to raise_error(Errors::CodeLimitReachedError)
    end
    
    it "raises error for inactive code" do
      code.update!(status: :disabled)
      expect { code.redeem!(free_user) }.to raise_error(Errors::CodeNotActiveError)
    end
    
    it "raises error when user already has premium access" do
      expect { code.redeem!(premium_user) }.to raise_error(Errors::UserAlreadyPremiumError)
    end
    
    it "sets no expiration for pilot tier upgrade" do
      pilot_code = create(:referral_code, tier_upgrade_to: :pilot, created_by: admin_user)
      pilot_code.redeem!(free_user)
      expect(free_user.reload.subscription_expires_at).to be_nil
      expect(free_user.subscription_tier).to eq("pilot")
    end
    
    context "race condition protection" do
      let(:multi_use_code) { create(:referral_code, max_uses: 1, current_uses: 0, created_by: admin_user) }
      
      it "handles concurrent redemptions correctly" do
        users = Array.new(2) { create(:user, subscription_tier: :free) }
        
        threads = users.map do |test_user|
          Thread.new do
            begin
              multi_use_code.reload.redeem!(test_user)
              :success
            rescue => e
              :failure
            end
          end
        end
        
        results = threads.map(&:value)
        expect(results.count(:success)).to eq(1)
        expect(results.count(:failure)).to eq(1)
        expect(multi_use_code.reload.current_uses).to eq(1)
      end
    end
  end
  
  describe "database constraints" do
    it "enforces current_uses <= max_uses constraint" do
      code = create(:referral_code, max_uses: 1, current_uses: 0, created_by: admin_user)
      
      expect {
        code.update_column(:current_uses, 2)  # Bypass validations to test DB constraint
      }.to raise_error(ActiveRecord::StatementInvalid)
    end
  end
end
